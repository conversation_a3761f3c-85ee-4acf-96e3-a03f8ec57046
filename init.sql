-- Initialize the Supreme Court database
-- This file is automatically executed when the PostgreSQL container starts

-- Create the main database (already created by environment variables)
-- CREATE DATABASE supreme_court_db;

-- Connect to the database
\c supreme_court_db;

-- Create extension for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the decisions table
CREATE TABLE IF NOT EXISTS decisions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    decision_id VARCHAR(50) UNIQUE NOT NULL,
    daire VARCHAR(100),
    esas_no VARCHAR(50),
    karar_no VARCHAR(50),
    decision_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create the scraping_progress table
CREATE TABLE IF NOT EXISTS scraping_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    page_number INTEGER NOT NULL,
    page_size INTEGER NOT NULL,
    decision_index INTEGER NOT NULL DEFAULT 0,
    total_decisions_in_page INTEGER NOT NULL DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create the failed_decisions table
CREATE TABLE IF NOT EXISTS failed_decisions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    decision_id VARCHAR(50) NOT NULL,
    page_number INTEGER NOT NULL,
    failure_reason VARCHAR(255) NOT NULL,
    error_details TEXT,
    attempt_count INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_decisions_decision_id ON decisions(decision_id);
CREATE INDEX IF NOT EXISTS idx_decisions_daire ON decisions(daire);
CREATE INDEX IF NOT EXISTS idx_decisions_esas_no ON decisions(esas_no);
CREATE INDEX IF NOT EXISTS idx_decisions_created_at ON decisions(created_at);

CREATE INDEX IF NOT EXISTS idx_scraping_progress_page_number ON scraping_progress(page_number);
CREATE INDEX IF NOT EXISTS idx_scraping_progress_is_completed ON scraping_progress(is_completed);
CREATE INDEX IF NOT EXISTS idx_scraping_progress_created_at ON scraping_progress(created_at);

CREATE INDEX IF NOT EXISTS idx_failed_decisions_decision_id ON failed_decisions(decision_id);
CREATE INDEX IF NOT EXISTS idx_failed_decisions_page_number ON failed_decisions(page_number);
CREATE INDEX IF NOT EXISTS idx_failed_decisions_failure_reason ON failed_decisions(failure_reason);
CREATE INDEX IF NOT EXISTS idx_failed_decisions_created_at ON failed_decisions(created_at);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_decisions_decision_id ON decisions(decision_id);
CREATE INDEX IF NOT EXISTS idx_decisions_daire ON decisions(daire);
CREATE INDEX IF NOT EXISTS idx_decisions_esas_no ON decisions(esas_no);
CREATE INDEX IF NOT EXISTS idx_decisions_karar_no ON decisions(karar_no);
CREATE INDEX IF NOT EXISTS idx_decisions_created_at ON decisions(created_at);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_decisions_updated_at 
    BEFORE UPDATE ON decisions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions to the scraper user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO scraper_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO scraper_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO scraper_user;
