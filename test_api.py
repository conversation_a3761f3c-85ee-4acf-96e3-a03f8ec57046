#!/usr/bin/env python3
"""
Test script for the Supreme Court Decisions Scraper API
"""
import requests
import time
import json

API_BASE_URL = "http://localhost:8000"

def test_api():
    """Test the API endpoints"""
    
    print("🧪 Testing Supreme Court Decisions Scraper API")
    print("=" * 50)
    
    # Test root endpoint
    print("\n1. Testing root endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/")
        print(f"✅ Root endpoint: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"❌ Root endpoint failed: {e}")
        return
    
    # Test stats endpoint
    print("\n2. Testing stats endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/stats")
        print(f"✅ Stats endpoint: {response.status_code}")
        stats = response.json()
        print(f"   Total decisions: {stats['database_stats']['total_decisions']}")
        print(f"   Completed pages: {stats['scraping_stats']['completed_pages']}")
        print(f"   Incomplete pages: {stats['scraping_stats']['incomplete_pages']}")
    except Exception as e:
        print(f"❌ Stats endpoint failed: {e}")
    
    # Test scraping a page
    print("\n3. Testing scrape endpoint...")
    try:
        scrape_data = {
            "page_number": 1,
            "page_size": 5  # Small size for testing
        }
        response = requests.post(f"{API_BASE_URL}/scrape", json=scrape_data)
        print(f"✅ Scrape endpoint: {response.status_code}")
        result = response.json()
        print(f"   Message: {result['message']}")
        print(f"   Page: {result['page_number']}")
        print(f"   Size: {result['page_size']}")
        
        page_number = result['page_number']
        
        # Wait a bit and check progress
        print(f"\n4. Checking progress for page {page_number}...")
        time.sleep(2)
        
        for i in range(10):  # Check progress up to 10 times
            try:
                response = requests.get(f"{API_BASE_URL}/progress/{page_number}")
                if response.status_code == 200:
                    progress = response.json()
                    print(f"   Progress: {progress['decision_index']}/{progress['total_decisions_in_page']} "
                          f"(Completed: {progress['is_completed']})")
                    
                    if progress['is_completed']:
                        print("   ✅ Page completed!")
                        break
                else:
                    print(f"   ⚠️ Progress check failed: {response.status_code}")
                
                time.sleep(3)  # Wait 3 seconds between checks
            except Exception as e:
                print(f"   ❌ Progress check error: {e}")
                break
        
    except Exception as e:
        print(f"❌ Scrape endpoint failed: {e}")
    
    # Test resume endpoint
    print("\n5. Testing resume endpoint...")
    try:
        response = requests.post(f"{API_BASE_URL}/resume")
        print(f"✅ Resume endpoint: {response.status_code}")
        result = response.json()
        print(f"   Message: {result['message']}")
    except Exception as e:
        print(f"❌ Resume endpoint failed: {e}")
    
    # Final stats
    print("\n6. Final stats...")
    try:
        response = requests.get(f"{API_BASE_URL}/stats")
        stats = response.json()
        print(f"   Total decisions: {stats['database_stats']['total_decisions']}")
        print(f"   Completed pages: {stats['scraping_stats']['completed_pages']}")
        print(f"   Recent decisions:")
        for decision in stats['database_stats']['recent_decisions'][:3]:
            print(f"     • {decision['decision_id']}: {decision['daire']}")
    except Exception as e:
        print(f"❌ Final stats failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API test completed!")

def scrape_multiple_pages(start_page: int, end_page: int, page_size: int = 10):
    """Scrape multiple pages using the API"""
    
    print(f"🚀 Scraping pages {start_page} to {end_page} with size {page_size}")
    print("=" * 60)
    
    for page in range(start_page, end_page + 1):
        print(f"\n📄 Starting page {page}...")
        
        try:
            scrape_data = {
                "page_number": page,
                "page_size": page_size
            }
            response = requests.post(f"{API_BASE_URL}/scrape", json=scrape_data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ {result['message']}")
            else:
                print(f"   ❌ Failed to start scraping: {response.status_code}")
                print(f"   Response: {response.text}")
        
        except Exception as e:
            print(f"   ❌ Error starting page {page}: {e}")
        
        # Small delay between requests
        time.sleep(1)
    
    print(f"\n✅ All pages ({start_page}-{end_page}) have been queued for scraping!")
    print("Use the progress endpoints to monitor their status.")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "multi":
            # Scrape multiple pages
            start = int(sys.argv[2]) if len(sys.argv) > 2 else 1
            end = int(sys.argv[3]) if len(sys.argv) > 3 else 5
            size = int(sys.argv[4]) if len(sys.argv) > 4 else 10
            scrape_multiple_pages(start, end, size)
        else:
            print("Usage:")
            print("  python test_api.py          # Run basic API tests")
            print("  python test_api.py multi [start] [end] [size]  # Scrape multiple pages")
    else:
        test_api()
