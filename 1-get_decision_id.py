import requests
import json
import time

def get_decision_ids(page_number=1, page_size=100, max_retries=3):
    url = 'https://karararama.yargitay.gov.tr/aramadetaylist'
    headers = {'Content-Type': 'application/json'}

    # Tüm kurulları, hukuk dairelerini ve ceza dairelerini seç
    # Web sitesindeki "Detaylı Arama" formundaki tüm seçenekleri taklit ediyoruz

    # Tüm Kurullar
    all_kurullar = [
        "Büyük Genel Kurulu",
        "Hukuk Genel Kurulu",
        "Ceza Genel Kurulu",
        "Hukuk Daireleri Başkanlar Kurulu",
        "Ceza Daireleri Başkanlar Kurulu"
    ]

    # Tüm Hukuk Daireleri (1-23)
    all_hukuk_daireleri = [f"{i}. Hukuk Dairesi" for i in range(1, 24)]

    # Tüm Ceza Daireleri (1-23)
    all_ceza_daireleri = [f"{i}. Ceza Dairesi" for i in range(1, 24)]

    payload = {
        "data": {
            "arananKelime": "",  # Boş - tüm kararları al
            "yargitayMah": "",   # Boş - tüm mahkemeleri dahil et
            "hukuk": "",         # Boş - tüm hukuk dairelerini dahil et
            "ceza": "",          # Boş - tüm ceza dairelerini dahil et
            "esasYil": "",
            "esasIlkSiraNo": "",
            "esasSonSiraNo": "",
            "kararYil": "",
            "kararIlkSiraNo": "",
            "kararSonSiraNo": "",
            "baslangicTarihi": "",
            "bitisTarihi": "",
            "siralama": "1",
            "siralamaDirection": "desc",
            # Tüm kurulları seç
            "birimYrgKurulDaire": "+".join(all_kurullar),
            # Tüm hukuk dairelerini seç
            "birimYrgHukukDaire": "+".join(all_hukuk_daireleri),
            # Tüm ceza dairelerini seç
            "birimYrgCezaDaire": "+".join(all_ceza_daireleri),
            "pageSize": page_size,
            "pageNumber": page_number
        }
    }

    # Retry mekanizması
    for attempt in range(max_retries):
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            break
        except requests.exceptions.Timeout:
            print(f"⚠️ Sayfa {page_number}: Zaman aşımı (deneme {attempt + 1}/{max_retries})")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
                continue
            else:
                print(f"❌ Sayfa {page_number}: Maksimum deneme sayısına ulaşıldı")
                return []
        except requests.exceptions.RequestException as e:
            print(f"⚠️ Sayfa {page_number}: İstek hatası (deneme {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
                continue
            else:
                print(f"❌ Sayfa {page_number}: Maksimum deneme sayısına ulaşıldı")
                return []

    data = response.json()

    # API yanıtını kontrol et
    if not data or "data" not in data:
        print(f"❌ Sayfa {page_number}: API yanıtında 'data' alanı bulunamadı")
        return []

    if data["data"] is None:
        print(f"❌ Sayfa {page_number}: API yanıtında 'data' alanı None")
        return []

    if "data" not in data["data"]:
        print(f"❌ Sayfa {page_number}: API yanıtında 'data.data' alanı bulunamadı")
        return []

    items = data["data"]["data"]
    if items is None:
        print(f"❌ Sayfa {page_number}: Sayfa verisi None")
        return []

    if not isinstance(items, list):
        print(f"❌ Sayfa {page_number}: Beklenen liste formatında değil: {type(items)}")
        return []

    # ID'leri çıkar
    decision_ids = []
    for item in items:
        if isinstance(item, dict) and "id" in item:
            decision_ids.append(item["id"])
        else:
            print(f"⚠️ Sayfa {page_number}: Geçersiz item formatı: {item}")

    return decision_ids


if __name__ == "__main__":
    print("1. Karar ID'lerini getirme testi başlıyor...")
    try:
        ids = get_decision_ids(page_number=1, page_size=10)
        print(f"✅ Başarılı! {len(ids)} adet karar ID'si alındı:")
        for i, decision_id in enumerate(ids[:5], 1):
            print(f"  {i}. {decision_id}")
        if len(ids) > 5:
            print(f"  ... ve {len(ids)-5} tane daha")
    except Exception as e:
        print(f"❌ Hata: {e}")
