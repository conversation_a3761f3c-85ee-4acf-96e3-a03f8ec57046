# Supreme Court Decisions Scraper

A Python application that scrapes Turkish Supreme Court (Yargıtay) decisions and saves them both in JSON format and PostgreSQL database.

## Features

- Scrapes decision IDs from the Supreme Court database
- Fetches detailed HTML content for each decision
- Parses and extracts structured data (court chamber, case numbers, decision text)
- Saves data in JSONL format for easy processing
- **NEW**: Stores data in PostgreSQL database with full-text search capabilities
- **NEW**: Database management utilities and statistics
- **NEW**: pgAdmin interface for database administration
- **NEW**: FastAPI REST API for controlled scraping with progress tracking
- **NEW**: Resume functionality to continue from where scraping left off
- Dockerized for easy deployment and consistent environment

## Project Structure

```
├── 1-get_decision_id.py    # Fetches decision IDs from search API
├── 2-get_decision_data.py  # Retrieves HTML content for decisions
├── 3-parse_html.py         # Parses HTML and extracts structured data
├── 4-save_for_json.py      # Orchestrates the full scraping process
├── main.py                 # Main entry point
├── api.py                  # FastAPI REST API server
├── test_api.py            # API testing script
├── database.py             # Database models and connection management
├── db_utils.py             # Database utilities and CLI tools
├── requirements.txt        # Python dependencies
├── Dockerfile             # Docker image configuration
├── docker-compose.yml     # Docker Compose setup with PostgreSQL and API
├── init.sql               # Database initialization script
├── Makefile              # Convenient commands for development
└── output/               # Directory for scraped data (created automatically)
```

## Quick Start with Docker

### Prerequisites
- Docker
- Docker Compose
- Make (optional, for convenient commands)

### Services Included
- **scraper**: Main Python application
- **api**: FastAPI REST API server (port 8000)
- **postgres**: PostgreSQL 15 database
- **pgadmin**: Web-based database administration (optional)

### Using Makefile (Recommended)

1. **Setup environment and database:**
   ```bash
   make setup
   ```

2. **Start the API server:**
   ```bash
   make api
   ```

3. **Start everything (database + API):**
   ```bash
   make start-all
   ```

4. **View API logs:**
   ```bash
   make api-logs
   ```

5. **Stop all services:**
   ```bash
   make down
   ```

### Manual Docker Commands

1. **Build the image:**
   ```bash
   docker-compose build
   ```

2. **Run the scraper:**
   ```bash
   docker-compose up scraper
   ```

3. **Run in background:**
   ```bash
   docker-compose up -d scraper
   ```

## Available Make Commands

Run `make help` to see all available commands:

### Main Commands
- `make setup` - Setup environment and database only
- `make api` - Start the FastAPI server
- `make start-all` - Start database and API together
- `make down` - Stop and remove containers
- `make logs` - Show scraper logs
- `make status` - Show container status

### Database Commands
- `make db-up` - Start only the database
- `make db-down` - Stop the database
- `make db-logs` - Show database logs
- `make db-shell` - Connect to PostgreSQL shell
- `make db-reset` - Reset the database (WARNING: deletes all data)
- `make db-stats` - Show database statistics
- `make db-recent` - Show recent decisions
- `make pgadmin` - Start pgAdmin for database management

### API Commands
- `make api` - Start the FastAPI server
- `make api-logs` - Show API logs

### Development Commands
- `make build` - Build the Docker image
- `make shell` - Open a shell in the container
- `make dev` - Start development environment
- `make test` - Run individual module tests
- `make test-db` - Test database connection
- `make clean` - Clean up Docker resources

## Local Development

### Without Docker

1. **Install dependencies:**
   ```bash
   make install-deps
   # or manually: pip install -r requirements.txt
   ```

2. **Run the scraper:**
   ```bash
   make run-local
   # or manually: python main.py
   ```

3. **Run tests:**
   ```bash
   make test-local
   ```

### With Docker Development Environment

1. **Start development container:**
   ```bash
   make dev
   ```

2. **Run individual modules for testing:**
   ```bash
   make test
   ```

## FastAPI REST API

The project includes a FastAPI REST API for controlled scraping with progress tracking and resume functionality.

### Starting the API

1. **Start the API server:**
   ```bash
   make api
   ```

2. **Access the API:**
   - **API Base URL**: http://localhost:8000
   - **Interactive Docs**: http://localhost:8000/docs
   - **OpenAPI Schema**: http://localhost:8000/openapi.json

### API Endpoints

#### 1. Root Endpoint
```bash
GET /
```
Returns API information and available endpoints.

#### 2. Scrape a Page
```bash
POST /scrape
Content-Type: application/json

{
  "page_number": 1,
  "page_size": 100
}
```
Starts scraping a specific page in the background. Returns immediately with task status.

#### 3. Check Progress
```bash
GET /progress/{page_number}
```
Returns the current progress for a specific page.

#### 4. Resume Scraping
```bash
POST /resume
```
Resumes scraping from the last incomplete page.

#### 5. Get Statistics
```bash
GET /stats
```
Returns database and scraping statistics.

### API Usage Examples

#### Using curl:

```bash
# Start scraping page 1 with 50 decisions
curl -X POST "http://localhost:8000/scrape" \
     -H "Content-Type: application/json" \
     -d '{"page_number": 1, "page_size": 50}'

# Check progress for page 1
curl "http://localhost:8000/progress/1"

# Resume scraping
curl -X POST "http://localhost:8000/resume"

# Get statistics
curl "http://localhost:8000/stats"
```

#### Using the test script:

```bash
# Run basic API tests
python test_api.py

# Scrape multiple pages (pages 1-5 with 10 decisions each)
python test_api.py multi 1 5 10
```

### API Features

- **Background Processing**: Scraping runs in background tasks, API responds immediately
- **Progress Tracking**: Track progress for each page with decision-level granularity
- **Resume Functionality**: Automatically resume from where scraping left off
- **Duplicate Prevention**: Skips decisions that already exist in the database
- **Error Handling**: Robust error handling with retry mechanisms
- **Rate Limiting**: Built-in delays to avoid overwhelming the source server

## Configuration

The scraper is configured to:
- Scrape pages 1-100 by default (configurable in `main.py`)
- Save data to both `output/yargitay_kararlar.jsonl` and PostgreSQL database
- Include a 0.2-second delay between requests to avoid overwhelming the server

### Database Configuration
- **Database**: PostgreSQL 15
- **Host**: postgres (container name)
- **Port**: 5432
- **Database**: supreme_court_db
- **User**: scraper_user
- **Password**: scraper_password

To modify the scraping parameters, edit the `main.py` file:
```python
save_module.crawl_and_save(
    start_page=1,
    end_page=100,
    output_file=output_file,
    save_to_db=True
)
```

## Output Format

The scraper saves data in JSONL format (one JSON object per line):

```json
{
  "id": "12345",
  "daire": "10. Hukuk Dairesi",
  "esas_no": "2025/6458",
  "karar_no": "2025/6461",
  "text": "Full decision text..."
}
```

## Data Management

### File Storage
- **Output Location:** All scraped data is saved to the `./output/` directory
- **Persistence:** The output directory is mounted as a Docker volume, so data persists between container restarts
- **Backup:** Use `make backup-data` to create timestamped backups

### Database Storage
- **Database Volume:** PostgreSQL data is stored in a Docker volume `postgres_data`
- **Persistence:** Database data persists between container restarts
- **Access:** Use `make db-shell` to access PostgreSQL directly
- **Management:** Use `make pgadmin` to access web-based database administration

### Database Utilities

Use the `db_utils.py` script for advanced database operations:

```bash
# Show database statistics
docker-compose run --rm scraper python db_utils.py stats

# Search decisions by text content
docker-compose run --rm scraper python db_utils.py search "keyword"

# Export decisions to JSON file
docker-compose run --rm scraper python db_utils.py export --output export.jsonl --limit 1000
```

### pgAdmin Access
When pgAdmin is running (`make pgadmin`):
- **URL**: http://localhost:8080
- **Email**: <EMAIL>
- **Password**: admin
- **Server**: postgres
- **Database**: supreme_court_db
- **Username**: scraper_user
- **Password**: scraper_password

## Troubleshooting

1. **Permission Issues:**
   ```bash
   sudo chown -R $USER:$USER ./output/
   ```

2. **Container Issues:**
   ```bash
   make clean  # Clean up Docker resources
   make build  # Rebuild the image
   ```

3. **View Container Logs:**
   ```bash
   make logs
   ```

4. **Debug in Container:**
   ```bash
   make shell
   ```

## Notes

- The scraper includes rate limiting (0.2s delay) to be respectful to the source website
- Total pages available: ~95,941 (as noted in the code)
- Each page typically contains 100 decisions
- The scraper handles errors gracefully and continues processing
