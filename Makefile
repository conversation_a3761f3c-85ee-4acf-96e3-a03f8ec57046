# Supreme Court Decisions Scraper - Makefile

.PHONY: help build up down logs shell clean test run-local install-deps db-up db-down db-logs db-shell pgadmin

# Default target
help: ## Show this help message
	@echo "Supreme Court Decisions Scraper with PostgreSQL"
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Docker commands
build: ## Build the Docker image
	docker-compose build


up: ## Start the scraper and database in detached mode
	docker-compose up -d

run: ## Run the scraper (foreground) with database
	docker-compose up

setup: ## Setup environment and database only
	docker-compose up scraper

api: ## Start the FastAPI server
	docker-compose up -d api
	@echo "API available at http://localhost:8000"
	@echo "API docs available at http://localhost:8000/docs"

api-logs: ## Show API logs
	docker-compose logs -f api

start-all: ## Start database and API together
	docker-compose up -d postgres api
	@echo "Database and API started"
	@echo "API available at http://localhost:8000"
	@echo "API docs available at http://localhost:8000/docs"

down: ## Stop and remove containers
	docker-compose down

logs: ## Show logs from the scraper
	docker-compose logs -f scraper

shell: ## Open a shell in the scraper container
	docker-compose run --rm scraper-dev

dev: ## Start development environment with shell access
	docker-compose --profile dev up -d
	docker-compose --profile dev exec scraper-dev /bin/bash

# Database commands
db-up: ## Start only the database
	docker-compose up -d postgres

db-down: ## Stop the database
	docker-compose stop postgres

db-logs: ## Show database logs
	docker-compose logs -f postgres

db-shell: ## Connect to PostgreSQL shell
	docker-compose exec postgres psql -U scraper_user -d supreme_court_db

db-reset: ## Reset the database (WARNING: deletes all data)
	docker-compose down -v
	docker volume rm scrape-supreme-court-decisions_postgres_data 2>/dev/null || true
	docker-compose up -d postgres

pgadmin: ## Start pgAdmin for database management
	docker-compose --profile admin up -d pgadmin
	@echo "pgAdmin available at http://localhost:8080"
	@echo "Email: <EMAIL>, Password: admin"

# Utility commands
clean: ## Clean up Docker resources
	docker-compose down -v
	docker system prune -f
	docker volume prune -f

test: ## Run individual test modules
	@echo "Running module tests..."
	docker-compose up -d postgres
	@echo "Waiting for database to be ready..."
	@sleep 5
	docker-compose run --rm scraper python 1-get_decision_id.py
	docker-compose run --rm scraper python 2-get_decision_data.py
	docker-compose run --rm scraper python 3-parse_html.py
	docker-compose run --rm scraper python 4-save_for_json.py

test-db: ## Test database connection and operations
	docker-compose up -d postgres
	@echo "Waiting for database to be ready..."
	@sleep 5
	docker-compose run --rm scraper python database.py

# Local development commands (without Docker)
install-deps: ## Install Python dependencies locally
	pip install -r requirements.txt

run-local: ## Run the scraper locally (requires Python and deps)
	python main.py

test-local: ## Run tests locally
	@echo "Running module tests locally..."
	python 1-get_decision_id.py
	python 2-get_decision_data.py
	python 3-parse_html.py
	python 4-save_for_json.py

# Data management
check-output: ## Check the output directory
	@echo "Output directory contents:"
	@ls -la ./output/ 2>/dev/null || echo "Output directory is empty or doesn't exist"

backup-data: ## Backup scraped data with timestamp
	@mkdir -p backups
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	if [ -d "./output" ] && [ "$$(ls -A ./output)" ]; then \
		tar -czf "backups/scraped_data_$$timestamp.tar.gz" ./output/; \
		echo "Data backed up to backups/scraped_data_$$timestamp.tar.gz"; \
	else \
		echo "No data to backup in ./output directory"; \
	fi

# Quick start
start: build up ## Build and start the scraper with database

restart: down up ## Restart the scraper and database

status: ## Show container status
	docker-compose ps

# Database queries
db-stats: ## Show database statistics
	docker-compose exec postgres psql -U scraper_user -d supreme_court_db -c "SELECT COUNT(*) as total_decisions FROM decisions;"
	docker-compose exec postgres psql -U scraper_user -d supreme_court_db -c "SELECT daire, COUNT(*) as count FROM decisions GROUP BY daire ORDER BY count DESC LIMIT 10;"

db-recent: ## Show recent decisions
	docker-compose exec postgres psql -U scraper_user -d supreme_court_db -c "SELECT decision_id, daire, esas_no, karar_no, created_at FROM decisions ORDER BY created_at DESC LIMIT 10;"
