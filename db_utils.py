#!/usr/bin/env python3
"""
Database utilities for Supreme Court decisions scraper
"""
import sys
import argparse
from database import db_manager, init_database, Decision, FailedDecision
from sqlalchemy import func

def show_stats():
    """Show database statistics"""
    try:
        total = db_manager.get_decisions_count()
        print(f"📊 Total decisions in database: {total}")
        
        if total > 0:
            with db_manager.get_session() as session:
                # Count by daire
                daire_stats = session.query(
                    Decision.daire, 
                    func.count(Decision.id).label('count')
                ).group_by(Decision.daire).order_by(func.count(Decision.id).desc()).limit(10).all()
                
                print("\n🏛️  Top 10 Daireler by decision count:")
                for daire, count in daire_stats:
                    print(f"   {daire}: {count}")
                
                # Recent decisions
                recent = db_manager.get_recent_decisions(5)
                print("\n📋 Recent decisions:")
                for decision in recent:
                    print(f"   {decision.decision_id}: {decision.daire} - {decision.created_at}")
                    
    except Exception as e:
        print(f"❌ Error getting stats: {e}")

def search_decisions(query):
    """Search decisions by text content"""
    try:
        with db_manager.get_session() as session:
            results = session.query(Decision).filter(
                Decision.decision_text.ilike(f'%{query}%')
            ).limit(10).all()
            
            print(f"🔍 Found {len(results)} decisions containing '{query}':")
            for decision in results:
                text_preview = decision.decision_text[:100] + "..." if decision.decision_text else "No text"
                print(f"   {decision.decision_id}: {decision.daire}")
                print(f"      {text_preview}")
                print()
                
    except Exception as e:
        print(f"❌ Error searching: {e}")

def export_to_json(output_file="export.jsonl", limit=None):
    """Export decisions to JSON file"""
    import json
    
    try:
        with db_manager.get_session() as session:
            query = session.query(Decision).order_by(Decision.created_at.desc())
            if limit:
                query = query.limit(limit)
            
            decisions = query.all()
            
            with open(output_file, 'w', encoding='utf-8') as f:
                for decision in decisions:
                    data = {
                        'id': decision.decision_id,
                        'daire': decision.daire,
                        'esas_no': decision.esas_no,
                        'karar_no': decision.karar_no,
                        'text': decision.decision_text,
                        'created_at': decision.created_at.isoformat() if decision.created_at else None
                    }
                    f.write(json.dumps(data, ensure_ascii=False) + '\n')
            
            print(f"✅ Exported {len(decisions)} decisions to {output_file}")
            
    except Exception as e:
        print(f"❌ Error exporting: {e}")

def show_failed_stats():
    """Show failed decisions statistics"""
    try:
        total_failed = db_manager.get_failed_decisions_count()
        print(f"❌ Total failed decisions: {total_failed}")

        if total_failed > 0:
            # Failed by reason
            failed_by_reason = db_manager.get_failed_decisions_by_reason(10)
            print("\n📊 Failed decisions by reason:")
            for reason, count in failed_by_reason:
                print(f"   {reason}: {count}")

            # Recent failed decisions
            recent_failed = db_manager.get_recent_failed_decisions(5)
            print("\n🔴 Recent failed decisions:")
            for failed in recent_failed:
                print(f"   {failed.decision_id} (Page {failed.page_number}): {failed.failure_reason}")
                if failed.error_details:
                    print(f"      Details: {failed.error_details[:100]}...")
                print(f"      Attempts: {failed.attempt_count}, Last: {failed.updated_at}")
                print()

    except Exception as e:
        print(f"❌ Error getting failed stats: {e}")

def export_failed_decisions(output_file="failed_decisions.jsonl"):
    """Export failed decisions to JSON file"""
    import json

    try:
        with db_manager.get_session() as session:
            failed_decisions = session.query(FailedDecision).order_by(FailedDecision.created_at.desc()).all()

            with open(output_file, 'w', encoding='utf-8') as f:
                for failed in failed_decisions:
                    data = {
                        'decision_id': failed.decision_id,
                        'page_number': failed.page_number,
                        'failure_reason': failed.failure_reason,
                        'error_details': failed.error_details,
                        'attempt_count': failed.attempt_count,
                        'created_at': failed.created_at.isoformat() if failed.created_at else None,
                        'updated_at': failed.updated_at.isoformat() if failed.updated_at else None
                    }
                    f.write(json.dumps(data, ensure_ascii=False) + '\n')

            print(f"✅ Exported {len(failed_decisions)} failed decisions to {output_file}")

    except Exception as e:
        print(f"❌ Error exporting failed decisions: {e}")

def main():
    parser = argparse.ArgumentParser(description='Database utilities for Supreme Court scraper')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Stats command
    subparsers.add_parser('stats', help='Show database statistics')
    
    # Search command
    search_parser = subparsers.add_parser('search', help='Search decisions')
    search_parser.add_argument('query', help='Search query')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export decisions to JSON')
    export_parser.add_argument('--output', '-o', default='export.jsonl', help='Output file')
    export_parser.add_argument('--limit', '-l', type=int, help='Limit number of records')

    # Failed stats command
    subparsers.add_parser('failed-stats', help='Show failed decisions statistics')

    # Export failed command
    export_failed_parser = subparsers.add_parser('export-failed', help='Export failed decisions to JSON')
    export_failed_parser.add_argument('--output', '-o', default='failed_decisions.jsonl', help='Output file')

    # Init command
    subparsers.add_parser('init', help='Initialize database')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'init':
            init_database()
            print("✅ Database initialized successfully")
            
        elif args.command == 'stats':
            show_stats()
            
        elif args.command == 'search':
            search_decisions(args.query)
            
        elif args.command == 'export':
            export_to_json(args.output, args.limit)

        elif args.command == 'failed-stats':
            show_failed_stats()

        elif args.command == 'export-failed':
            export_failed_decisions(args.output)
            
    except Exception as e:
        print(f"❌ Command failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
