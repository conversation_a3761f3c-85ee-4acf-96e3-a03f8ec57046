"""
FastAPI application for Supreme Court decisions scraper
"""
import asyncio
import time
from typing import Optional, List, Dict, Any
from fastapi import FastAPI, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel
from database import db_manager, init_database, ScrapingProgress
from importlib import import_module

# Initialize FastAPI app
app = FastAPI(
    title="Supreme Court Decisions Scraper API",
    description="API for scraping Turkish Supreme Court (Yargıtay) decisions",
    version="1.0.0"
)

# Pydantic models for API
class ScrapingRequest(BaseModel):
    page_number: Optional[int] = None  # If None, auto-continue from last position
    page_size: int = 100

class ScrapingResponse(BaseModel):
    success: bool
    message: str
    page_number: int
    page_size: int
    total_decisions_found: int
    decisions_processed: int
    is_completed: bool

class ProgressResponse(BaseModel):
    page_number: int
    page_size: int
    decision_index: int
    total_decisions_in_page: int
    is_completed: bool
    created_at: str
    updated_at: str

# Global variable to track running scraping tasks
running_tasks: Dict[int, bool] = {}

@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    try:
        init_database()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Supreme Court Decisions Scraper API",
        "version": "1.0.0",
        "endpoints": {
            "scrape": "/scrape",
            "start_continuous": "/start-continuous",
            "progress": "/progress/{page_number}",
            "resume": "/resume",
            "stats": "/stats",
            "failed_decisions": "/failed-decisions"
        }
    }

@app.post("/scrape", response_model=ScrapingResponse)
async def scrape_page(request: ScrapingRequest, background_tasks: BackgroundTasks):
    """
    Start scraping - either a specific page or auto-continue from last position
    """
    page_number = request.page_number
    page_size = request.page_size

    # If no page number specified, find the next page to scrape
    if page_number is None:
        # First check if there's an incomplete page to resume
        last_incomplete = db_manager.get_last_incomplete_page()
        if last_incomplete:
            page_number = last_incomplete.page_number
            print(f"🔄 Resuming incomplete page {page_number}")
        else:
            # Find the next page after the last completed page
            with db_manager.get_session() as session:
                last_completed = session.query(ScrapingProgress).filter_by(
                    is_completed=True
                ).order_by(ScrapingProgress.page_number.desc()).first()

                if last_completed:
                    page_number = last_completed.page_number + 1
                    print(f"🚀 Starting next page after completed: {page_number}")
                else:
                    page_number = 1
                    print(f"🚀 Starting from page 1 (no previous progress found)")

    # Check if this page is already being processed
    if page_number in running_tasks and running_tasks[page_number]:
        raise HTTPException(
            status_code=409,
            detail=f"Page {page_number} is already being processed"
        )

    # Check if this page is already completed
    existing_progress = db_manager.get_scraping_progress(page_number)
    if existing_progress and existing_progress.is_completed:
        # If this page is completed, automatically move to next page
        next_page = page_number + 1
        print(f"✅ Page {page_number} already completed, moving to page {next_page}")

        # Check if next page is already being processed
        if next_page in running_tasks and running_tasks[next_page]:
            raise HTTPException(
                status_code=409,
                detail=f"Page {page_number} completed, but next page {next_page} is already being processed"
            )

        # Start next page
        background_tasks.add_task(scrape_page_background, next_page, page_size)

        return ScrapingResponse(
            success=True,
            message=f"Page {page_number} already completed, started page {next_page}",
            page_number=next_page,
            page_size=page_size,
            total_decisions_found=0,
            decisions_processed=0,
            is_completed=False
        )

    # Start background task for the determined page
    start_from_index = 0
    if existing_progress and not existing_progress.is_completed:
        start_from_index = existing_progress.decision_index
        print(f"🔄 Resuming page {page_number} from decision {start_from_index}")

    background_tasks.add_task(scrape_page_background, page_number, page_size, start_from_index)

    return ScrapingResponse(
        success=True,
        message=f"Started scraping page {page_number}" + (f" from decision {start_from_index}" if start_from_index > 0 else ""),
        page_number=page_number,
        page_size=page_size,
        total_decisions_found=0,
        decisions_processed=start_from_index,
        is_completed=False
    )

@app.get("/progress/{page_number}", response_model=ProgressResponse)
async def get_progress(page_number: int):
    """
    Get scraping progress for a specific page
    """
    progress = db_manager.get_scraping_progress(page_number)
    if not progress:
        raise HTTPException(
            status_code=404, 
            detail=f"No progress found for page {page_number}"
        )
    
    return ProgressResponse(
        page_number=progress.page_number,
        page_size=progress.page_size,
        decision_index=progress.decision_index,
        total_decisions_in_page=progress.total_decisions_in_page,
        is_completed=progress.is_completed,
        created_at=progress.created_at.isoformat(),
        updated_at=progress.updated_at.isoformat()
    )

@app.post("/resume")
async def resume_scraping(background_tasks: BackgroundTasks):
    """
    Resume scraping from the last incomplete page
    """
    last_incomplete = db_manager.get_last_incomplete_page()
    if not last_incomplete:
        return {
            "success": False,
            "message": "No incomplete pages found to resume"
        }
    
    page_number = last_incomplete.page_number
    
    # Check if this page is already being processed
    if page_number in running_tasks and running_tasks[page_number]:
        raise HTTPException(
            status_code=409, 
            detail=f"Page {page_number} is already being processed"
        )
    
    # Start background task from where it left off
    background_tasks.add_task(
        scrape_page_background, 
        page_number, 
        last_incomplete.page_size,
        last_incomplete.decision_index
    )
    
    return {
        "success": True,
        "message": f"Resumed scraping page {page_number} from decision {last_incomplete.decision_index}",
        "page_number": page_number,
        "page_size": last_incomplete.page_size,
        "resume_from_index": last_incomplete.decision_index
    }

@app.get("/stats")
async def get_stats():
    """
    Get database and scraping statistics
    """
    try:
        total_decisions = db_manager.get_decisions_count()
        recent_decisions = db_manager.get_recent_decisions(5)
        
        # Get scraping progress stats
        with db_manager.get_session() as session:
            total_pages_started = session.query(ScrapingProgress).count()
            completed_pages = session.query(ScrapingProgress).filter_by(is_completed=True).count()
            incomplete_pages = session.query(ScrapingProgress).filter_by(is_completed=False).count()

        # Get failed decisions stats
        total_failed = db_manager.get_failed_decisions_count()
        failed_by_reason = db_manager.get_failed_decisions_by_reason(5)
        recent_failed = db_manager.get_recent_failed_decisions(3)
        
        return {
            "database_stats": {
                "total_decisions": total_decisions,
                "recent_decisions": [
                    {
                        "decision_id": d.decision_id,
                        "daire": d.daire,
                        "esas_no": d.esas_no,
                        "karar_no": d.karar_no,
                        "created_at": d.created_at.isoformat()
                    } for d in recent_decisions
                ]
            },
            "scraping_stats": {
                "total_pages_started": total_pages_started,
                "completed_pages": completed_pages,
                "incomplete_pages": incomplete_pages,
                "currently_running": len([k for k, v in running_tasks.items() if v])
            },
            "failed_decisions_stats": {
                "total_failed": total_failed,
                "failed_by_reason": [
                    {
                        "reason": reason,
                        "count": count
                    } for reason, count in failed_by_reason
                ],
                "recent_failed": [
                    {
                        "decision_id": f.decision_id,
                        "page_number": f.page_number,
                        "failure_reason": f.failure_reason,
                        "attempt_count": f.attempt_count,
                        "created_at": f.created_at.isoformat()
                    } for f in recent_failed
                ]
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting stats: {str(e)}")

@app.get("/failed-decisions")
async def get_failed_decisions(limit: int = Query(20, ge=1, le=100)):
    """
    Get failed decisions with details
    """
    try:
        failed_decisions = db_manager.get_recent_failed_decisions(limit)
        failed_by_reason = db_manager.get_failed_decisions_by_reason(10)
        total_failed = db_manager.get_failed_decisions_count()

        return {
            "total_failed": total_failed,
            "failed_by_reason": [
                {
                    "reason": reason,
                    "count": count
                } for reason, count in failed_by_reason
            ],
            "recent_failed_decisions": [
                {
                    "decision_id": f.decision_id,
                    "page_number": f.page_number,
                    "failure_reason": f.failure_reason,
                    "error_details": f.error_details,
                    "attempt_count": f.attempt_count,
                    "created_at": f.created_at.isoformat(),
                    "updated_at": f.updated_at.isoformat()
                } for f in failed_decisions
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting failed decisions: {str(e)}")

@app.post("/start-continuous")
async def start_continuous_scraping(
    background_tasks: BackgroundTasks,
    page_size: int = Query(100, ge=1, le=500)
):
    """
    Start continuous scraping from where it left off
    This will automatically continue to next pages as they complete

    Rate limiting:
    - 0.1 seconds between decisions
    - 3 seconds between pages
    - No retries - failed decisions are skipped immediately
    """
    try:
        # Find where to start
        last_incomplete = db_manager.get_last_incomplete_page()
        if last_incomplete:
            start_page = last_incomplete.page_number
            start_index = last_incomplete.decision_index
            message = f"Resuming from page {start_page}, decision {start_index}"
        else:
            # Find the next page after the last completed page
            with db_manager.get_session() as session:
                last_completed = session.query(ScrapingProgress).filter_by(
                    is_completed=True
                ).order_by(ScrapingProgress.page_number.desc()).first()

                if last_completed:
                    start_page = last_completed.page_number + 1
                    start_index = 0
                    message = f"Starting from page {start_page} (next after last completed)"
                else:
                    start_page = 1
                    start_index = 0
                    message = "Starting from page 1 (no previous progress)"

        # Check if already running
        if start_page in running_tasks and running_tasks[start_page]:
            raise HTTPException(
                status_code=409,
                detail=f"Continuous scraping already running on page {start_page}"
            )

        # Start the continuous scraping
        background_tasks.add_task(scrape_page_background, start_page, page_size, start_index)

        return {
            "success": True,
            "message": message,
            "start_page": start_page,
            "start_index": start_index,
            "page_size": page_size,
            "rate_limiting": {
                "decision_delay": "0.1 seconds",
                "page_delay": "3 seconds",
                "retry_policy": "no retries - skip failed decisions immediately"
            },
            "note": "Scraping will automatically continue to next pages as they complete"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting continuous scraping: {str(e)}")

async def scrape_page_background(page_number: int, page_size: int, start_from_index: int = 0):
    """
    Background task to scrape a page and process all decisions
    """
    running_tasks[page_number] = True
    
    try:
        # Import modules
        get_ids_module = import_module("1-get_decision_id")
        get_data_module = import_module("2-get_decision_data")
        parse_module = import_module("3-parse_html")
        
        print(f"🚀 Starting scraping page {page_number} (size: {page_size}) from index {start_from_index}")
        
        # Get decision IDs for this page
        decision_ids = get_ids_module.get_decision_ids(page_number, page_size)
        
        if not decision_ids:
            print(f"❌ No decision IDs found for page {page_number}")
            return
        
        total_decisions = len(decision_ids)
        print(f"📋 Found {total_decisions} decision IDs for page {page_number}")
        
        # Save initial progress
        db_manager.save_scraping_progress(
            page_number=page_number,
            page_size=page_size,
            decision_index=start_from_index,
            total_decisions=total_decisions,
            is_completed=False
        )
        
        # Process each decision starting from the specified index
        for i, decision_id in enumerate(decision_ids[start_from_index:], start=start_from_index):
            try:
                print(f"🔄 Processing decision {i+1}/{total_decisions}: {decision_id}")
                
                # Check if decision already exists in database
                existing_decision = db_manager.get_decision_by_id(decision_id)
                if existing_decision:
                    print(f"⏭️  Decision {decision_id} already exists, skipping...")
                    # Update progress
                    db_manager.save_scraping_progress(
                        page_number=page_number,
                        page_size=page_size,
                        decision_index=i + 1,
                        total_decisions=total_decisions,
                        is_completed=False
                    )
                    continue
                
                # Get HTML content (single attempt, no retry)
                try:
                    html_content = get_data_module.get_decision_html(decision_id)
                    if not html_content:
                        print(f"⚠️ No HTML content for decision {decision_id} - skipping")
                        # Save failed decision to database
                        db_manager.save_failed_decision(
                            decision_id=decision_id,
                            page_number=page_number,
                            failure_reason="HTML içeriği bulunamadı",
                            error_details="API'den HTML içeriği alınamadı"
                        )
                        continue
                except Exception as e:
                    print(f"❌ Error getting HTML for decision {decision_id}: {e} - skipping")
                    # Save failed decision to database
                    db_manager.save_failed_decision(
                        decision_id=decision_id,
                        page_number=page_number,
                        failure_reason="HTML alma hatası - exception",
                        error_details=str(e)
                    )
                    continue
                
                # Parse HTML
                try:
                    parsed_data = parse_module.parse_decision_html(html_content)

                    # Check if parsing was successful (has meaningful data)
                    if not parsed_data or not any([parsed_data.get('daire'), parsed_data.get('text')]):
                        print(f"⚠️ Parse failed for decision {decision_id} - no meaningful data extracted")
                        db_manager.save_failed_decision(
                            decision_id=decision_id,
                            page_number=page_number,
                            failure_reason="Parse hatası - anlamlı veri çıkarılamadı",
                            error_details="HTML parse edildi ancak daire veya metin bilgisi bulunamadı"
                        )
                        continue

                    # Prepare decision data for database
                    decision_data = {
                        'id': decision_id,
                        'daire': parsed_data.get('daire', ''),
                        'esas_no': parsed_data.get('esas_no', ''),
                        'karar_no': parsed_data.get('karar_no', ''),
                        'text': parsed_data.get('text', '')
                    }
                except Exception as parse_error:
                    print(f"❌ Parse error for decision {decision_id}: {parse_error}")
                    db_manager.save_failed_decision(
                        decision_id=decision_id,
                        page_number=page_number,
                        failure_reason="Parse hatası - exception",
                        error_details=str(parse_error)
                    )
                    continue
                
                # Save to database
                try:
                    saved_decision = db_manager.save_decision(decision_data)
                    if saved_decision:
                        print(f"✅ Saved decision {decision_id}: {saved_decision.daire}")
                    else:
                        print(f"❌ Failed to save decision {decision_id}")
                        db_manager.save_failed_decision(
                            decision_id=decision_id,
                            page_number=page_number,
                            failure_reason="Database kayıt hatası",
                            error_details="Karar veritabanına kaydedilemedi"
                        )
                except Exception as save_error:
                    print(f"❌ Database save error for decision {decision_id}: {save_error}")
                    db_manager.save_failed_decision(
                        decision_id=decision_id,
                        page_number=page_number,
                        failure_reason="Database kayıt hatası - exception",
                        error_details=str(save_error)
                    )
                
                # Update progress
                db_manager.save_scraping_progress(
                    page_number=page_number,
                    page_size=page_size,
                    decision_index=i + 1,
                    total_decisions=total_decisions,
                    is_completed=False
                )
                
                # Small delay between decisions
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f"❌ Error processing decision {decision_id}: {e}")
                continue
        
        # Mark page as completed
        db_manager.save_scraping_progress(
            page_number=page_number,
            page_size=page_size,
            decision_index=total_decisions,
            total_decisions=total_decisions,
            is_completed=True
        )

        print(f"✅ Completed scraping page {page_number}")

        # Auto-start next page if this one completed successfully
        next_page = page_number + 1
        print(f"🚀 Auto-starting next page: {next_page}")

        # Wait 3 seconds before starting next page
        print(f"⏳ Waiting 3 seconds before starting page {next_page}...")
        await asyncio.sleep(3)

        # Check if next page is already being processed
        if next_page not in running_tasks or not running_tasks[next_page]:
            # Check if next page is already completed
            next_progress = db_manager.get_scraping_progress(next_page)
            if not next_progress or not next_progress.is_completed:
                # Start next page in background
                asyncio.create_task(scrape_page_background(next_page, page_size, 0))
                print(f"✅ Auto-started page {next_page}")
            else:
                print(f"⏭️  Page {next_page} already completed, skipping")
        else:
            print(f"⚠️ Page {next_page} already being processed")

    except Exception as e:
        print(f"❌ Error in background scraping task for page {page_number}: {e}")
    finally:
        running_tasks[page_number] = False

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
