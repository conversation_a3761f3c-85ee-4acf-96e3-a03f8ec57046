"""
FastAPI application for Supreme Court decisions scraper
"""
import asyncio
import time
from typing import Optional, List, Dict, Any
from fastapi import FastAPI, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel
from database import db_manager, init_database, ScrapingProgress
from importlib import import_module

# Initialize FastAPI app
app = FastAPI(
    title="Supreme Court Decisions Scraper API",
    description="API for scraping Turkish Supreme Court (Yargıtay) decisions",
    version="1.0.0"
)

# Pydantic models for API
class ScrapingRequest(BaseModel):
    page_number: int
    page_size: int = 100

class ScrapingResponse(BaseModel):
    success: bool
    message: str
    page_number: int
    page_size: int
    total_decisions_found: int
    decisions_processed: int
    is_completed: bool

class ProgressResponse(BaseModel):
    page_number: int
    page_size: int
    decision_index: int
    total_decisions_in_page: int
    is_completed: bool
    created_at: str
    updated_at: str

# Global variable to track running scraping tasks
running_tasks: Dict[int, bool] = {}

@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    try:
        init_database()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Supreme Court Decisions Scraper API",
        "version": "1.0.0",
        "endpoints": {
            "scrape": "/scrape",
            "progress": "/progress/{page_number}",
            "resume": "/resume",
            "stats": "/stats"
        }
    }

@app.post("/scrape", response_model=ScrapingResponse)
async def scrape_page(request: ScrapingRequest, background_tasks: BackgroundTasks):
    """
    Start scraping a specific page
    """
    page_number = request.page_number
    page_size = request.page_size
    
    # Check if this page is already being processed
    if page_number in running_tasks and running_tasks[page_number]:
        raise HTTPException(
            status_code=409, 
            detail=f"Page {page_number} is already being processed"
        )
    
    # Check if this page is already completed
    existing_progress = db_manager.get_scraping_progress(page_number)
    if existing_progress and existing_progress.is_completed:
        return ScrapingResponse(
            success=True,
            message=f"Page {page_number} already completed",
            page_number=page_number,
            page_size=page_size,
            total_decisions_found=existing_progress.total_decisions_in_page,
            decisions_processed=existing_progress.decision_index,
            is_completed=True
        )
    
    # Start background task
    background_tasks.add_task(scrape_page_background, page_number, page_size)
    
    return ScrapingResponse(
        success=True,
        message=f"Started scraping page {page_number}",
        page_number=page_number,
        page_size=page_size,
        total_decisions_found=0,
        decisions_processed=0,
        is_completed=False
    )

@app.get("/progress/{page_number}", response_model=ProgressResponse)
async def get_progress(page_number: int):
    """
    Get scraping progress for a specific page
    """
    progress = db_manager.get_scraping_progress(page_number)
    if not progress:
        raise HTTPException(
            status_code=404, 
            detail=f"No progress found for page {page_number}"
        )
    
    return ProgressResponse(
        page_number=progress.page_number,
        page_size=progress.page_size,
        decision_index=progress.decision_index,
        total_decisions_in_page=progress.total_decisions_in_page,
        is_completed=progress.is_completed,
        created_at=progress.created_at.isoformat(),
        updated_at=progress.updated_at.isoformat()
    )

@app.post("/resume")
async def resume_scraping(background_tasks: BackgroundTasks):
    """
    Resume scraping from the last incomplete page
    """
    last_incomplete = db_manager.get_last_incomplete_page()
    if not last_incomplete:
        return {
            "success": False,
            "message": "No incomplete pages found to resume"
        }
    
    page_number = last_incomplete.page_number
    
    # Check if this page is already being processed
    if page_number in running_tasks and running_tasks[page_number]:
        raise HTTPException(
            status_code=409, 
            detail=f"Page {page_number} is already being processed"
        )
    
    # Start background task from where it left off
    background_tasks.add_task(
        scrape_page_background, 
        page_number, 
        last_incomplete.page_size,
        last_incomplete.decision_index
    )
    
    return {
        "success": True,
        "message": f"Resumed scraping page {page_number} from decision {last_incomplete.decision_index}",
        "page_number": page_number,
        "page_size": last_incomplete.page_size,
        "resume_from_index": last_incomplete.decision_index
    }

@app.get("/stats")
async def get_stats():
    """
    Get database and scraping statistics
    """
    try:
        total_decisions = db_manager.get_decisions_count()
        recent_decisions = db_manager.get_recent_decisions(5)
        
        # Get scraping progress stats
        with db_manager.get_session() as session:
            total_pages_started = session.query(ScrapingProgress).count()
            completed_pages = session.query(ScrapingProgress).filter_by(is_completed=True).count()
            incomplete_pages = session.query(ScrapingProgress).filter_by(is_completed=False).count()
        
        return {
            "database_stats": {
                "total_decisions": total_decisions,
                "recent_decisions": [
                    {
                        "decision_id": d.decision_id,
                        "daire": d.daire,
                        "esas_no": d.esas_no,
                        "karar_no": d.karar_no,
                        "created_at": d.created_at.isoformat()
                    } for d in recent_decisions
                ]
            },
            "scraping_stats": {
                "total_pages_started": total_pages_started,
                "completed_pages": completed_pages,
                "incomplete_pages": incomplete_pages,
                "currently_running": len([k for k, v in running_tasks.items() if v])
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting stats: {str(e)}")

async def scrape_page_background(page_number: int, page_size: int, start_from_index: int = 0):
    """
    Background task to scrape a page and process all decisions
    """
    running_tasks[page_number] = True
    
    try:
        # Import modules
        get_ids_module = import_module("1-get_decision_id")
        get_data_module = import_module("2-get_decision_data")
        parse_module = import_module("3-parse_html")
        
        print(f"🚀 Starting scraping page {page_number} (size: {page_size}) from index {start_from_index}")
        
        # Get decision IDs for this page
        decision_ids = get_ids_module.get_decision_ids(page_number, page_size)
        
        if not decision_ids:
            print(f"❌ No decision IDs found for page {page_number}")
            return
        
        total_decisions = len(decision_ids)
        print(f"📋 Found {total_decisions} decision IDs for page {page_number}")
        
        # Save initial progress
        db_manager.save_scraping_progress(
            page_number=page_number,
            page_size=page_size,
            decision_index=start_from_index,
            total_decisions=total_decisions,
            is_completed=False
        )
        
        # Process each decision starting from the specified index
        for i, decision_id in enumerate(decision_ids[start_from_index:], start=start_from_index):
            try:
                print(f"🔄 Processing decision {i+1}/{total_decisions}: {decision_id}")
                
                # Check if decision already exists in database
                existing_decision = db_manager.get_decision_by_id(decision_id)
                if existing_decision:
                    print(f"⏭️  Decision {decision_id} already exists, skipping...")
                    # Update progress
                    db_manager.save_scraping_progress(
                        page_number=page_number,
                        page_size=page_size,
                        decision_index=i + 1,
                        total_decisions=total_decisions,
                        is_completed=False
                    )
                    continue
                
                # Get HTML content
                html_content = get_data_module.get_decision_html(decision_id)
                if not html_content:
                    print(f"⚠️ No HTML content for decision {decision_id}")
                    continue
                
                # Parse HTML
                parsed_data = parse_module.parse_decision_html(html_content)
                
                # Prepare decision data for database
                decision_data = {
                    'id': decision_id,
                    'daire': parsed_data.get('daire', ''),
                    'esas_no': parsed_data.get('esas_no', ''),
                    'karar_no': parsed_data.get('karar_no', ''),
                    'text': parsed_data.get('text', '')
                }
                
                # Save to database
                saved_decision = db_manager.save_decision(decision_data)
                if saved_decision:
                    print(f"✅ Saved decision {decision_id}: {saved_decision.daire}")
                else:
                    print(f"❌ Failed to save decision {decision_id}")
                
                # Update progress
                db_manager.save_scraping_progress(
                    page_number=page_number,
                    page_size=page_size,
                    decision_index=i + 1,
                    total_decisions=total_decisions,
                    is_completed=False
                )
                
                # Small delay to avoid overwhelming the server
                await asyncio.sleep(0.2)
                
            except Exception as e:
                print(f"❌ Error processing decision {decision_id}: {e}")
                continue
        
        # Mark page as completed
        db_manager.save_scraping_progress(
            page_number=page_number,
            page_size=page_size,
            decision_index=total_decisions,
            total_decisions=total_decisions,
            is_completed=True
        )
        
        print(f"✅ Completed scraping page {page_number}")
        
    except Exception as e:
        print(f"❌ Error in background scraping task for page {page_number}: {e}")
    finally:
        running_tasks[page_number] = False

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
