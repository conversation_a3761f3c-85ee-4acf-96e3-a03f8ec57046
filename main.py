import os
from database import db_manager, init_database

def setup_environment():
    """Setup the environment and database"""
    print("🚀 Supreme Court Decisions Scraper - Environment Setup")
    print("TÜM DAİRELERDEN kararları alacak:")
    print("  📋 <PERSON>ü<PERSON> (Büyük Genel Kurulu, Hukuk/Ceza Genel Kurulu, vb.)")
    print("  ⚖️  Tüm Hukuk Daireleri (1-23)")
    print("  🔨 Tüm Ceza Daireleri (1-23)")
    print("Veriler PostgreSQL veritabanına kaydedilecek...")

    # Create output directory if it doesn't exist
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 Output directory: {output_dir}")

    # Initialize database
    try:
        init_database()
        print("✅ Database initialized successfully")

        # Show current stats
        total_in_db = db_manager.get_decisions_count()
        print(f"🗄️  Veritabanında mevcut karar sayısı: {total_in_db}")

        if total_in_db > 0:
            recent = db_manager.get_recent_decisions(3)
            if recent:
                print("📋 Son eklenen kararlar:")
                for decision in recent:
                    print(f"   • {decision.decision_id}: {decision.daire}")

        print("\n" + "="*60)
        print("✅ Environment setup completed!")
        print("🌐 API kullanarak scraping başlatabilirsiniz:")
        print("   • API Server: make api")
        print("   • API Docs: http://localhost:8000/docs")
        print("   • Test Script: python test_api.py")
        print("="*60)

    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    setup_environment()