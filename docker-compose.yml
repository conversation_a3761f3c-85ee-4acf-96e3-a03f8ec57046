version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: supreme-court-postgres
    environment:
      POSTGRES_DB: supreme_court_db
      POSTGRES_USER: scraper_user
      POSTGRES_PASSWORD: scraper_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5420:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U scraper_user -d supreme_court_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  scraper:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: supreme-court-scraper
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      # Mount output directory to persist scraped data
      - ./output:/app/output
      # Mount source code for development (optional)
      - ./:/app
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=********************************************************/supreme_court_db
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=supreme_court_db
      - DB_USER=scraper_user
      - DB_PASSWORD=scraper_password
    restart: "no"  # Don't restart automatically
    command: python main.py  # Just setup environment, don't start scraping

  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: supreme-court-api
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "8000:8000"
    volumes:
      # Mount output directory to persist scraped data
      - ./output:/app/output
      # Mount source code for development (optional)
      - ./:/app
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=********************************************************/supreme_court_db
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=supreme_court_db
      - DB_USER=scraper_user
      - DB_PASSWORD=scraper_password
    restart: unless-stopped
    command: uvicorn api:app --host 0.0.0.0 --port 8000 --reload


volumes:
  postgres_data:
