"""
Database connection and models for Supreme Court decisions scraper
"""
import os
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import create_engine, Column, String, Text, DateTime, UUID, text, Integer, Boolean
from sqlalchemy.orm import declarative_base, sessionmaker, Session
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = os.getenv(
    'DATABASE_URL', 
    'postgresql://scraper_user:scraper_password@localhost:5432/supreme_court_db'
)

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()


class Decision(Base):
    """
    SQLAlchemy model for Supreme Court decisions
    """
    __tablename__ = "decisions"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    decision_id = Column(String(50), unique=True, nullable=False, index=True)
    daire = Column(String(100), index=True)
    esas_no = Column(String(50), index=True)
    karar_no = Column(String(50), index=True)
    decision_text = Column(Text)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<Decision(decision_id='{self.decision_id}', daire='{self.daire}', esas_no='{self.esas_no}')>"


class ScrapingProgress(Base):
    """
    SQLAlchemy model for tracking scraping progress
    """
    __tablename__ = "scraping_progress"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    page_number = Column(Integer, nullable=False, index=True)
    page_size = Column(Integer, nullable=False)
    decision_index = Column(Integer, nullable=False, default=0)  # Which decision in the page we're processing
    total_decisions_in_page = Column(Integer, nullable=False, default=0)
    is_completed = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<ScrapingProgress(page={self.page_number}, index={self.decision_index}/{self.total_decisions_in_page}, completed={self.is_completed})>"


class FailedDecision(Base):
    """
    SQLAlchemy model for tracking failed decision scraping attempts
    """
    __tablename__ = "failed_decisions"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    decision_id = Column(String(50), nullable=False, index=True)
    page_number = Column(Integer, nullable=False, index=True)
    failure_reason = Column(String(255), nullable=False)  # e.g., "HTML içeriği bulunamadı", "Parse hatası", etc.
    error_details = Column(Text)  # Detailed error message if available
    attempt_count = Column(Integer, default=1)  # How many times we tried
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<FailedDecision(decision_id='{self.decision_id}', reason='{self.failure_reason}', attempts={self.attempt_count})>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert the decision to a dictionary"""
        return {
            'id': str(self.id),
            'decision_id': self.decision_id,
            'daire': self.daire,
            'esas_no': self.esas_no,
            'karar_no': self.karar_no,
            'decision_text': self.decision_text,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class DatabaseManager:
    """
    Database manager class for handling database operations
    """
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
        
    def get_session(self) -> Session:
        """Get a database session"""
        return self.SessionLocal()
    
    def create_tables(self):
        """Create all tables"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            raise
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.get_session() as session:
                session.execute(text("SELECT 1"))
                logger.info("Database connection successful")
                return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False
    
    def save_decision(self, decision_data: Dict[str, Any]) -> Optional[Decision]:
        """
        Save a decision to the database
        
        Args:
            decision_data: Dictionary containing decision information
            
        Returns:
            Decision object if successful, None otherwise
        """
        try:
            with self.get_session() as session:
                # Check if decision already exists
                existing = session.query(Decision).filter_by(
                    decision_id=decision_data.get('id')
                ).first()
                
                if existing:
                    logger.info(f"Decision {decision_data.get('id')} already exists, skipping")
                    return existing
                
                # Create new decision
                decision = Decision(
                    decision_id=decision_data.get('id'),
                    daire=decision_data.get('daire'),
                    esas_no=decision_data.get('esas_no'),
                    karar_no=decision_data.get('karar_no'),
                    decision_text=decision_data.get('text')
                )
                
                session.add(decision)
                session.commit()
                session.refresh(decision)
                
                logger.info(f"Decision {decision.decision_id} saved successfully")
                return decision
                
        except Exception as e:
            logger.error(f"Error saving decision {decision_data.get('id')}: {e}")
            return None
    
    def get_decision_by_id(self, decision_id: str) -> Optional[Decision]:
        """Get a decision by its ID"""
        try:
            with self.get_session() as session:
                return session.query(Decision).filter_by(decision_id=decision_id).first()
        except Exception as e:
            logger.error(f"Error getting decision {decision_id}: {e}")
            return None
    
    def get_decisions_count(self) -> int:
        """Get total number of decisions in database"""
        try:
            with self.get_session() as session:
                return session.query(Decision).count()
        except Exception as e:
            logger.error(f"Error getting decisions count: {e}")
            return 0
    
    def get_recent_decisions(self, limit: int = 10) -> List[Decision]:
        """Get recent decisions"""
        try:
            with self.get_session() as session:
                return session.query(Decision).order_by(
                    Decision.created_at.desc()
                ).limit(limit).all()
        except Exception as e:
            logger.error(f"Error getting recent decisions: {e}")
            return []

    def save_scraping_progress(self, page_number: int, page_size: int, decision_index: int, total_decisions: int, is_completed: bool = False) -> Optional[ScrapingProgress]:
        """Save or update scraping progress"""
        try:
            with self.get_session() as session:
                # Check if progress for this page already exists
                existing = session.query(ScrapingProgress).filter_by(page_number=page_number).first()

                if existing:
                    # Update existing progress
                    existing.decision_index = decision_index
                    existing.total_decisions_in_page = total_decisions
                    existing.is_completed = is_completed
                    existing.updated_at = datetime.utcnow()
                    progress = existing
                else:
                    # Create new progress record
                    progress = ScrapingProgress(
                        page_number=page_number,
                        page_size=page_size,
                        decision_index=decision_index,
                        total_decisions_in_page=total_decisions,
                        is_completed=is_completed
                    )
                    session.add(progress)

                session.commit()
                session.refresh(progress)
                logger.info(f"Scraping progress saved: page {page_number}, decision {decision_index}/{total_decisions}")
                return progress
        except Exception as e:
            logger.error(f"Error saving scraping progress: {e}")
            return None

    def get_scraping_progress(self, page_number: int) -> Optional[ScrapingProgress]:
        """Get scraping progress for a specific page"""
        try:
            with self.get_session() as session:
                return session.query(ScrapingProgress).filter_by(page_number=page_number).first()
        except Exception as e:
            logger.error(f"Error getting scraping progress: {e}")
            return None

    def get_last_incomplete_page(self) -> Optional[ScrapingProgress]:
        """Get the last incomplete page to resume scraping"""
        try:
            with self.get_session() as session:
                return session.query(ScrapingProgress).filter_by(
                    is_completed=False
                ).order_by(ScrapingProgress.page_number.desc()).first()
        except Exception as e:
            logger.error(f"Error getting last incomplete page: {e}")
            return None

    def save_failed_decision(self, decision_id: str, page_number: int, failure_reason: str, error_details: str = None) -> Optional[FailedDecision]:
        """Save a failed decision attempt"""
        try:
            with self.get_session() as session:
                # Check if this decision already failed before
                existing = session.query(FailedDecision).filter_by(
                    decision_id=decision_id
                ).first()

                if existing:
                    # Update existing record
                    existing.attempt_count += 1
                    existing.failure_reason = failure_reason
                    existing.error_details = error_details
                    existing.updated_at = datetime.utcnow()
                    failed_decision = existing
                else:
                    # Create new failed decision record
                    failed_decision = FailedDecision(
                        decision_id=decision_id,
                        page_number=page_number,
                        failure_reason=failure_reason,
                        error_details=error_details,
                        attempt_count=1
                    )
                    session.add(failed_decision)

                session.commit()
                session.refresh(failed_decision)
                logger.warning(f"Failed decision saved: {decision_id} - {failure_reason}")
                return failed_decision
        except Exception as e:
            logger.error(f"Error saving failed decision: {e}")
            return None

    def get_failed_decisions_count(self) -> int:
        """Get total count of failed decisions"""
        try:
            with self.get_session() as session:
                return session.query(FailedDecision).count()
        except Exception as e:
            logger.error(f"Error getting failed decisions count: {e}")
            return 0

    def get_failed_decisions_by_reason(self, limit: int = 10) -> List[tuple]:
        """Get failed decisions grouped by reason"""
        try:
            with self.get_session() as session:
                from sqlalchemy import func
                return session.query(
                    FailedDecision.failure_reason,
                    func.count(FailedDecision.id).label('count')
                ).group_by(FailedDecision.failure_reason).order_by(
                    func.count(FailedDecision.id).desc()
                ).limit(limit).all()
        except Exception as e:
            logger.error(f"Error getting failed decisions by reason: {e}")
            return []

    def get_recent_failed_decisions(self, limit: int = 10) -> List[FailedDecision]:
        """Get recent failed decisions"""
        try:
            with self.get_session() as session:
                return session.query(FailedDecision).order_by(
                    FailedDecision.created_at.desc()
                ).limit(limit).all()
        except Exception as e:
            logger.error(f"Error getting recent failed decisions: {e}")
            return []


# Global database manager instance
db_manager = DatabaseManager()


def init_database():
    """Initialize the database"""
    logger.info("Initializing database...")
    
    # Test connection
    if not db_manager.test_connection():
        raise Exception("Failed to connect to database")
    
    # Create tables
    db_manager.create_tables()
    
    logger.info("Database initialized successfully")


if __name__ == "__main__":
    # Test the database connection and setup
    print("Testing database connection...")
    
    try:
        init_database()
        
        # Test saving a sample decision
        sample_decision = {
            'id': 'test_123',
            'daire': 'Test Dairesi',
            'esas_no': '2025/1',
            'karar_no': '2025/1',
            'text': 'Test decision text'
        }
        
        saved = db_manager.save_decision(sample_decision)
        if saved:
            print(f"✅ Test decision saved: {saved}")
            
            # Test retrieval
            retrieved = db_manager.get_decision_by_id('test_123')
            if retrieved:
                print(f"✅ Test decision retrieved: {retrieved}")
            
            # Get count
            count = db_manager.get_decisions_count()
            print(f"✅ Total decisions in database: {count}")
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
